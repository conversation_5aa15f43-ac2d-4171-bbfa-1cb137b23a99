plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'com.google.gms.google-services'
    id 'dev.flutter.flutter-gradle-plugin'
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.masterkoi.bid"
    compileSdkVersion 35
    ndkVersion "27.0.12077973"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
        // Enable core library desugaring
        coreLibraryDesugaringEnabled true
    }

    // Add core library desugaring dependency
    dependencies {
        coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
        implementation 'com.google.android.play:core-ktx:1.8.1'
        implementation platform('com.google.firebase:firebase-bom:33.14.0')
        implementation 'com.google.firebase:firebase-messaging'
        implementation 'com.google.firebase:firebase-analytics'
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.masterkoi.bid"
        minSdkVersion 23
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    flavorDimensions "ikan"

    productFlavors {
        koifish {
            dimension "ikan"
            applicationId "com.masterkoi.bid"
            versionCode 181
            versionName "3.8.1"
            resValue "string", "app_name", "Master Koi"
        }
        goldfish {
            dimension "ikan"
            applicationId "com.goldfishop.bid"
            versionCode 7
            versionName "0.1.7"
            resValue "string", "app_name", "Gold Fish"
        }
        arwanafish {
            dimension "ikan"
            applicationId "com.aronesia.bid"
            versionCode 3
            versionName "1.0.3"
            resValue "string", "app_name", "Arwana Fish"
        }
    }
    packagingOptions {
        resources.excludes.add("META-INF/*")
        resources.pickFirsts.add("build-data.properties")
    }
    signingConfigs {
        koifishRelease {
            storeFile file('/Users/<USER>/Documents/MasterKoiApp/flutter/android/keystore')
            storePassword 'dayan123'
            keyAlias = 'dayan'
            keyPassword 'dayan123'
        }
        goldfishRelease {
            storeFile file('/Users/<USER>/Documents/MasterKoiApp/flutter/android/keystoreGoldfish')
            storePassword 'dayan123'
            keyAlias = 'dayan'
            keyPassword 'dayan123'
        }
        arwanafishRelease {
            storeFile file('/Users/<USER>/Documents/MasterKoiApp/flutter/android/keystoreArwana')
            storePassword 'dayan111'
            keyAlias = 'dayan'
            keyPassword 'dayan111'
        }
    }
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            productFlavors.koifish {
                signingConfig signingConfigs.koifishRelease
            }
            productFlavors.goldfish {
                signingConfig signingConfigs.goldfishRelease
            }
            productFlavors.arwanafish {
                signingConfig signingConfigs.arwanafishRelease
            }
        }
        debug {
            minifyEnabled false
        }
    }
}


