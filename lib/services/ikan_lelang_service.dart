import '../models/ikan_lelang.dart';
import '../utils/constants.dart';
import 'api_service.dart';

class IkanLelangService {
  final ApiService _apiService = ApiService();

  /// Get list of ikan lelang with optional filters
  Future<List<IkanLelang>> getIkanLelangList({
    int page = 1,
    String search = '',
    Map<String, dynamic>? filters,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page.toString(),
        'per_page': '10',
      };

      if (search.isNotEmpty) {
        queryParams['search'] = search;
      }

      if (filters != null) {
        filters.forEach((key, value) {
          queryParams[key] = value.toString();
        });
      }

      final response = await _apiService.get(
        AppConstants.ikanLelangEndpoint,
        queryParameters: queryParams.map((key, value) => MapEntry(key, value.toString())),
      );

      if (response['meta']['code'] == 200) {
        final List<dynamic> data = response['data'];
        return data.map((item) => IkanLelang.fromJson(item)).toList();
      } else {
        throw Exception(response['meta']['message'] ?? 'Failed to load ikan lelang');
      }
    } catch (e) {
      throw Exception('Error getting ikan lelang list: $e');
    }
  }

  /// Get detail of a specific ikan lelang
  Future<IkanLelang?> getIkanLelangDetail(String idObyekLelang) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.ikanDetailEndpoint}/$idObyekLelang',
      );

      if (response['meta']['code'] == 200) {
        return IkanLelang.fromJson(response['data']);
      } else {
        throw Exception(response['meta']['message'] ?? 'Failed to load ikan detail');
      }
    } catch (e) {
      throw Exception('Error getting ikan detail: $e');
    }
  }

  /// Add ikan to wishlist
  Future<Map<String, dynamic>> addToWishlist(String idObyekLelang) async {
    try {
      final response = await _apiService.post(
        AppConstants.wishlistAddEndpoint,
        body: {'id_obyek_lelang': idObyekLelang},
      );

      return {
        'success': response['meta']['code'] == 200,
        'message': response['meta']['message'] ?? 'Unknown error',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error adding to wishlist: $e',
      };
    }
  }

  /// Remove ikan from wishlist
  Future<Map<String, dynamic>> removeFromWishlist(String idObyekLelang) async {
    try {
      final response = await _apiService.post(
        AppConstants.wishlistRemoveEndpoint,
        body: {'id_obyek_lelang': idObyekLelang},
      );

      return {
        'success': response['meta']['code'] == 200,
        'message': response['meta']['message'] ?? 'Unknown error',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error removing from wishlist: $e',
      };
    }
  }

  /// Bid on an ikan
  Future<Map<String, dynamic>> bidIkan({
    required String idObyekLelang,
    required String command,
    required String pin,
  }) async {
    try {
      final response = await _apiService.post(
        AppConstants.bidIkanEndpoint,
        body: {
          'id_obyek_lelang': idObyekLelang,
          'command': command,
          'pin': pin,
        },
      );

      return response;
    } catch (e) {
      return {
        'meta': {
          'code': 500,
          'message': 'Error bidding ikan: $e',
        }
      };
    }
  }

  /// Get bidder history
  Future<String> getBidderHistori(String idBuyer) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.bidderHistoriEndpoint}/$idBuyer',
      );

      if (response['meta']['code'] == 200) {
        return response['data']['histori'] ?? '';
      } else {
        return '';
      }
    } catch (e) {
      return '';
    }
  }

  /// Get wishlist items
  Future<List<IkanLelang>> getWishlist({int page = 1}) async {
    try {
      final response = await _apiService.get(
        AppConstants.wishlistEndpoint,
        queryParameters: {
          'page': page.toString(),
          'per_page': '10',
        },
      );

      if (response['meta']['code'] == 200) {
        final List<dynamic> data = response['data'];
        return data.map((item) => IkanLelang.fromJson(item)).toList();
      } else {
        throw Exception(response['meta']['message'] ?? 'Failed to load wishlist');
      }
    } catch (e) {
      throw Exception('Error getting wishlist: $e');
    }
  }

  /// Get filter options for ikan lelang
  Future<Map<String, dynamic>> getFilterOptions() async {
    try {
      final response = await _apiService.get(
        AppConstants.ikanLiveStatistikEndpoint,
      );

      if (response['meta']['code'] == 200) {
        return response['data'] ?? {};
      } else {
        throw Exception(response['meta']['message'] ?? 'Failed to load filter options');
      }
    } catch (e) {
      throw Exception('Error getting filter options: $e');
    }
  }
}